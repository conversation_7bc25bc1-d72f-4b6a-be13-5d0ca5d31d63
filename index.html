<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>丰选智慧教培管理系统 - 让管理更简单，让业务更高效</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --accent-color: #ff6b6b;
            --text-dark: #2d3748;
            --text-light: #718096;
            --bg-light: #f8fafc;
            --white: #ffffff;
            --shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            --shadow-hover: 0 20px 60px rgba(0, 0, 0, 0.15);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            color: var(--text-dark);
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Navigation */
        .navbar {
            position: fixed;
            top: 0;
            width: 100%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            z-index: 1000;
            padding: 15px 0;
            transition: all 0.3s ease;
        }

        .navbar.scrolled {
            box-shadow: var(--shadow);
        }

        .nav-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 30px;
        }

        .nav-links a {
            text-decoration: none;
            color: var(--text-dark);
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-links a:hover {
            color: var(--primary-color);
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: var(--white);
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
            padding-top: 80px;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            text-align: center;
            animation: fadeInUp 1s ease-out;
        }

        .hero h1 {
            font-size: clamp(2.5rem, 5vw, 4rem);
            font-weight: 800;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #fff, #f0f8ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero .subtitle {
            font-size: clamp(1.2rem, 3vw, 1.8rem);
            margin-bottom: 2rem;
            opacity: 0.9;
            font-weight: 300;
        }

        .hero .description {
            font-size: clamp(1rem, 2vw, 1.3rem);
            margin-bottom: 3rem;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
            opacity: 0.8;
            line-height: 1.8;
        }

        .cta-button {
            display: inline-block;
            background: linear-gradient(45deg, var(--accent-color), #ee5a24);
            color: var(--white);
            padding: 18px 40px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
            margin: 10px;
        }

        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(255, 107, 107, 0.4);
        }

        /* Floating Elements */
        .floating-element {
            position: absolute;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
            font-size: 3rem;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(5deg); }
        }

        /* Value Proposition */
        .value-prop {
            padding: 100px 0;
            background: var(--bg-light);
        }

        .section-title {
            text-align: center;
            font-size: clamp(2rem, 4vw, 3rem);
            font-weight: 700;
            margin-bottom: 3rem;
            color: var(--text-dark);
        }

        .value-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
            margin-top: 60px;
        }

        .value-card {
            background: var(--white);
            padding: 40px 30px;
            border-radius: 20px;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            text-align: center;
        }

        .value-card:hover {
            transform: translateY(-10px);
            box-shadow: var(--shadow-hover);
        }

        .value-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            color: var(--primary-color);
        }

        /* Problem Solution Section */
        .problem-solution {
            padding: 100px 0;
            background: var(--white);
        }

        .scenario {
            margin-bottom: 80px;
            padding: 40px;
            border-radius: 20px;
            background: var(--bg-light);
        }

        .scenario h3 {
            font-size: 1.8rem;
            color: var(--text-dark);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .problem-box, .solution-box {
            margin: 30px 0;
            padding: 30px;
            border-radius: 15px;
            border-left: 5px solid;
        }

        .problem-box {
            background: #fff5f5;
            border-color: #fc8181;
        }

        .solution-box {
            background: #f0fff4;
            border-color: #68d391;
        }

        /* Features Grid */
        .features {
            padding: 100px 0;
            background: var(--bg-light);
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 40px;
            margin-top: 60px;
        }

        .feature-card {
            background: var(--white);
            padding: 40px 30px;
            border-radius: 20px;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            border: 1px solid #e2e8f0;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: var(--shadow-hover);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 25px;
            font-size: 2rem;
            color: var(--white);
        }

        /* Advantages */
        .advantages {
            padding: 100px 0;
            background: linear-gradient(135deg, var(--text-dark) 0%, #4a5568 100%);
            color: var(--white);
        }

        .advantages-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
            margin-top: 60px;
        }

        .advantage-card {
            text-align: center;
            padding: 30px;
        }

        .advantage-card h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #81e6d9;
        }

        /* CTA Section */
        .final-cta {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: var(--white);
            padding: 100px 0;
            text-align: center;
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.8s ease-out;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }
            
            .value-grid,
            .features-grid,
            .advantages-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }
            
            .scenario {
                padding: 20px;
            }
            
            .problem-box,
            .solution-box {
                padding: 20px;
            }
        }

        /* Scroll Progress Bar */
        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
            z-index: 1001;
            transition: width 0.1s ease;
        }
    </style>
</head>
<body>
    <!-- Progress Bar -->
    <div class="progress-bar"></div>

    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <div class="nav-content">
                <div class="logo">🎓 丰选智慧教培</div>
                <ul class="nav-links">
                    <li><a href="#hero">首页</a></li>
                    <li><a href="#problems">解决方案</a></li>
                    <li><a href="#features">功能模块</a></li>
                    <li><a href="#advantages">核心优势</a></li>
                    <li><a href="#contact">联系我们</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero" id="hero">
        <div class="floating-element" style="top: 10%; left: 10%; animation-delay: 0s;">🎓</div>
        <div class="floating-element" style="top: 20%; right: 15%; animation-delay: -2s;">📊</div>
        <div class="floating-element" style="bottom: 30%; left: 20%; animation-delay: -4s;">💡</div>
        <div class="floating-element" style="bottom: 20%; right: 25%; animation-delay: -1s;">🚀</div>
        
        <div class="container">
            <div class="hero-content">
                <h1>🎓 智慧教培管理系统</h1>
                <p class="subtitle">让管理更简单，让业务更高效</p>
                <p class="description">
                    为教培机构量身定制的数字化解决方案<br>
                    一个系统，解决教培机构管理全部难题<br>
                    从体验课到结业，从校区到班级，从财务到教务，从员工到学员——全流程数字化管理<br>
                    <strong>一套数据，电脑端和微信小程序端共用</strong>
                </p>
                <a href="#problems" class="cta-button">
                    <i class="fas fa-rocket"></i> 了解解决方案
                </a>
                <a href="#contact" class="cta-button">
                    <i class="fas fa-phone"></i> 立即咨询
                </a>
            </div>
        </div>
    </section>

    <!-- Core Value Proposition -->
    <section class="value-prop">
        <div class="container">
            <h2 class="section-title">🌟 产品核心价值</h2>
            <div class="value-grid">
                <div class="value-card fade-in-up">
                    <div class="value-icon">🎯</div>
                    <h3>一个系统解决全部难题</h3>
                    <p>从体验课到结业，从校区到班级，从财务到教务，从员工到学员——全流程数字化管理</p>
                </div>
                <div class="value-card fade-in-up">
                    <div class="value-icon">📱</div>
                    <h3>一套数据多端共享</h3>
                    <p>电脑端和微信小程序端共用同一套数据，随时随地管理您的教培机构</p>
                </div>
                <div class="value-card fade-in-up">
                    <div class="value-icon">🚀</div>
                    <h3>数字化转型利器</h3>
                    <p>告别传统管理模式，拥抱数字化未来，让您的教培机构更具竞争力</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Problem Solution Section -->
    <section class="problem-solution" id="problems">
        <div class="container">
            <h2 class="section-title">🎯 解决管理困境与挑战</h2>

            <!-- Scenario 1: Multi-campus Management -->
            <div class="scenario fade-in-up">
                <h3>😰 场景一：多校区管理的噩梦</h3>
                <div class="problem-box">
                    <h4>🚨 管理痛点</h4>
                    <p>某教育集团负责人曾面临多校区各自为政、信息孤立的困境，每天疲于应对来自各校区的频繁问题，难以掌握整体运营状况。</p>
                </div>
                <div class="solution-box">
                    <h4>😌 智慧教培系统如何解决？</h4>
                    <h5>🏢 多校区统一管理驾驶舱</h5>
                    <p>系统提供一个后台，实现多校区统一管理。实时监控在读学员、员工、合约金额等关键数据，并能精细化控制不同校区、不同角色的权限。</p>
                    <p><strong>💡 效果：</strong> 从每日处理大量管理电话，转变为仅需数分钟查看数据报表。</p>
                </div>
            </div>

            <!-- Scenario 2: Data Management -->
            <div class="scenario fade-in-up">
                <h3>😰 场景二：经营数据的黑盒子</h3>
                <div class="problem-box">
                    <h4>🚨 管理痛点</h4>
                    <p>月底统计数据时，数据分散且难以汇总，导致经营状况不清晰，决策缺乏依据。</p>
                </div>
                <div class="solution-box">
                    <h4>😌 智慧教培系统如何解决？</h4>
                    <h5>📊 数据驾驶舱：您的千里眼</h5>
                    <p><strong>实时经营数据监控：</strong>全面展示耗课进度、在读统计、合约统计、课时课酬等核心数据，确保您随时随地掌握经营状况。</p>
                    <p><strong>多维度分析报表：</strong>支持按校区、项目、时间等多维度统计，快速生成收入趋势、学员增长、员工绩效等报表。</p>
                    <p><strong>💡 效果：</strong> 告别漫长的数据等待，随时随地掌控经营全局。</p>
                </div>
            </div>

            <!-- Scenario 3: Staff Management -->
            <div class="scenario fade-in-up">
                <h3>😰 场景三：人员管理的头疼事</h3>
                <div class="problem-box">
                    <h4>🚨 管理痛点</h4>
                    <p>人员管理复杂，员工分布广、工作量不均、权限不清，导致团队效率受影响。</p>
                </div>
                <div class="solution-box">
                    <h4>😌 智慧教培系统如何解决？</h4>
                    <h5>👥 智能人员管理系统</h5>
                    <p><strong>完整员工档案体系：</strong>统一管理员工信息，明确校区分布、项目分配和工作量。</p>
                    <p><strong>权限管理精细化：</strong>根据角色设定精准权限，并记录所有操作日志，新员工入职可快速配置权限。</p>
                    <p><strong>💡 效果：</strong> 从依赖人工记忆，转向系统化科学管理。</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Academic Management Challenges -->
    <section class="problem-solution" style="background: var(--bg-light);">
        <div class="container">
            <h2 class="section-title">📚 解决教务主任的教学管理挑战</h2>

            <!-- Academic Scenario 1: Course Scheduling -->
            <div class="scenario fade-in-up">
                <h3>😰 场景一：排课就像玩俄罗斯方块</h3>
                <div class="problem-box">
                    <h4>🚨 教务痛点</h4>
                    <p>排课工作耗时耗力，常出现冲突，且难以应对临时变动。</p>
                </div>
                <div class="solution-box">
                    <h4>😌 智慧教培系统如何解决？</h4>
                    <h5>📅 可视化智能排课系统</h5>
                    <p><strong>拖拽式课表管理：</strong>提供可视化周课表，自动检测冲突，支持课表模板复制和临时调课，变动信息实时同步。</p>
                    <p><strong>💡 效果：</strong> 排课时间从数日缩短至数分钟。</p>
                </div>
            </div>

            <!-- Academic Scenario 2: Student Information -->
            <div class="scenario fade-in-up">
                <h3>😰 场景二：学员信息管理的混乱</h3>
                <div class="problem-box">
                    <h4>🚨 教务痛点</h4>
                    <p>学员信息分散，课时消耗和学习进度难以跟踪，家校沟通效率低下。</p>
                </div>
                <div class="solution-box">
                    <h4>😌 智慧教培系统如何解决？</h4>
                    <h5>👨‍🎓 全方位学员档案管理</h5>
                    <p><strong>完整学员档案：</strong>建立学员完整档案，追踪学习记录、成长轨迹和家校沟通历史，并进行智能统计分析，如流失率预警。</p>
                    <p><strong>💡 效果：</strong> 从查找信息耗时，到快速调出完整档案。</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Financial Management Challenges -->
    <section class="problem-solution">
        <div class="container">
            <h2 class="section-title">💰 解决财务主管的核算难题</h2>

            <!-- Financial Scenario 1: Payment Management -->
            <div class="scenario fade-in-up">
                <h3>😰 场景一：收费管理的复杂局面</h3>
                <div class="problem-box">
                    <h4>🚨 财务痛点</h4>
                    <p>多支付方式混杂，分期、优惠计算复杂，退费流程不规范，导致账目混乱。</p>
                </div>
                <div class="solution-box">
                    <h4>😌 智慧教培系统如何解决？</h4>
                    <h5>💳 精细化财务管理系统</h5>
                    <p><strong>多元支付管理：</strong>支持多种支付方式，精准管理缴费记录、优惠券和分期付款。实时监控收款进度，预警逾期账款。</p>
                    <p><strong>💡 效果：</strong> 从每月耗时对账，到随时查询精准财务状况。</p>
                </div>
            </div>

            <!-- Financial Scenario 2: Teacher Salary -->
            <div class="scenario fade-in-up">
                <h3>😰 场景二：课酬核算的头疼事</h3>
                <div class="problem-box">
                    <h4>🚨 财务痛点</h4>
                    <p>手工计算教师课酬耗时费力，标准不统一，且易引发争议。</p>
                </div>
                <div class="solution-box">
                    <h4>😌 智慧教培系统如何解决？</h4>
                    <h5>🧮 自动化课酬管理</h5>
                    <p><strong>智能课酬计算：</strong>自动统计教师工作量，支持多种课酬标准，并与考勤关联，确保课酬核算透明准确。</p>
                    <p><strong>💡 效果：</strong> 从人工计算数日，到系统数分钟自动生成。</p>
                </div>
            </div>

            <!-- Financial Scenario 3: Refund Management -->
            <div class="scenario fade-in-up">
                <h3>😰 场景三：退费处理的规范化</h3>
                <div class="problem-box">
                    <h4>🚨 财务痛点</h4>
                    <p>退费标准不统一，审批流程混乱，核算困难，导致纠纷频发。</p>
                </div>
                <div class="solution-box">
                    <h4>😌 智慧教培系统如何解决？</h4>
                    <h5>📋 标准化退费管理</h5>
                    <p><strong>规范退费流程：</strong>规范退费流程，精准核算退款金额，分析退费原因，并确保审批流程清晰可追溯。</p>
                    <p><strong>💡 效果：</strong> 从退费纠纷频发，到标准化透明化处理。</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Deep Dive -->
    <section class="features" id="features">
        <div class="container">
            <h2 class="section-title">🛠️ 功能模块深度解析</h2>

            <div class="features-grid">
                <div class="feature-card fade-in-up">
                    <div class="feature-icon">
                        <i class="fas fa-chart-dashboard"></i>
                    </div>
                    <h3>📊 数据统计模块</h3>
                    <h4>管理者决策驾驶舱</h4>
                    <p><strong>核心价值：</strong> 数据驱动决策，告别拍脑袋管理。</p>
                    <p>提供经营数据实时监控、业务健康度诊断，让决策有数据支撑，风险可控，目标可量化。</p>
                </div>

                <div class="feature-card fade-in-up">
                    <div class="feature-icon">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <h3>🎓 教务管理模块</h3>
                    <h4>教学质量的保障</h4>
                    <p><strong>核心价值：</strong> 标准化教学管理，提升教育品质。</p>
                    <p>实现班级管理标准化、学员档案数字化、课表管理智能化，优化教学质量，提升服务品质。</p>
                </div>

                <div class="feature-card fade-in-up">
                    <div class="feature-icon">
                        <i class="fas fa-file-contract"></i>
                    </div>
                    <h3>📝 合约管理模块</h3>
                    <h4>销售业绩的加速器</h4>
                    <p><strong>核心价值：</strong> 销售全流程管控，提升成交转化。</p>
                    <p>管理合约全生命周期，优化收款流程，规范退费管理，提升销售转化，降低资金风险。</p>
                </div>

                <div class="feature-card fade-in-up">
                    <div class="feature-icon">
                        <i class="fas fa-book"></i>
                    </div>
                    <h3>📚 课程管理模块</h3>
                    <h4>产品体系的基石</h4>
                    <p><strong>核心价值：</strong> 标准化产品体系，支撑业务扩张。</p>
                    <p>标准化课包产品、丰富课程内容、清晰项目分类，增强市场竞争力，便于业务复制。</p>
                </div>

                <div class="feature-card fade-in-up">
                    <div class="feature-icon">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <h3>💳 财务管理模块</h3>
                    <h4>资金安全的守护者</h4>
                    <p><strong>核心价值：</strong> 财务精细化管理，降低经营风险。</p>
                    <p>支持多元化支付管理，提供营销工具，自动化生成财务报表，确保资金安全，提升经营效率。</p>
                </div>

                <div class="feature-card fade-in-up">
                    <div class="feature-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3>👥 员工管理模块</h3>
                    <h4>人力资源的优化器</h4>
                    <p><strong>核心价值：</strong> 人力资源全生命周期管理。</p>
                    <p>数字化员工档案，精细化权限管理，灵活项目分配，提升管理效率，优化团队协作。</p>
                </div>

                <div class="feature-card fade-in-up">
                    <div class="feature-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <h3>🎯 体验课管理模块</h3>
                    <h4>招生转化的利器</h4>
                    <p><strong>核心价值：</strong> 招生漏斗管理，提升转化率。</p>
                    <p>管理体验课全流程，分析转化数据，支持精准营销和跟进提醒，有效提升招生效率。</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Core Advantages -->
    <section class="advantages" id="advantages">
        <div class="container">
            <h2 class="section-title">🎯 系统核心竞争优势</h2>

            <div class="advantages-grid">
                <div class="advantage-card fade-in-up">
                    <div class="value-icon">🏆</div>
                    <h3>行业领先的技术架构</h3>
                    <p><strong>云原生设计，安全可靠：</strong>基于成熟云平台，确保系统高可用性、数据加密存储和异地容灾备份，支持弹性扩容。</p>
                    <p><strong>移动优先，随时随地管理：</strong>支持多设备适配，提供手机端便捷使用。</p>
                </div>

                <div class="advantage-card fade-in-up">
                    <div class="value-icon">🎓</div>
                    <h3>深度理解教培行业</h3>
                    <p><strong>丰富行业经验沉淀：</strong>服务大量教培机构，覆盖多种业务场景，持续迭代产品。</p>
                    <p><strong>本地化服务优势：</strong>提供全天候技术支持，可定制化需求响应，并提供上门培训服务。</p>
                </div>

                <div class="advantage-card fade-in-up">
                    <div class="value-icon">🚀</div>
                    <h3>立即行动：开启数字化转型</h3>
                    <p><strong>专属解决方案：</strong>提供定制化技术开发服务，并配备专业顾问和数据迁移支持。</p>
                    <p><strong>快速实施，数天上线：</strong>标准化实施流程，确保系统在短期内完成配置、培训并正式上线。</p>
                    <p><strong>持续服务，陪伴成长：</strong>提供全天候技术支持，定期产品更新，并进行运营数据分析和经营诊断。</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Final CTA -->
    <section class="final-cta" id="contact">
        <div class="container">
            <h2>🚀 立即行动：开启您的数字化转型之旅</h2>
            <p class="subtitle">📞 联系我们，专业顾问为您服务</p>
            <p>立即预约免费演示 - 专业顾问将为您提供系统演示、需求分析、投资回报测算及实施方案规划</p>

            <div style="margin: 40px 0;">
                <a href="tel:************" class="cta-button">
                    <i class="fas fa-phone"></i> ************
                </a>
                <a href="#demo" class="cta-button">
                    <i class="fas fa-desktop"></i> 预约演示
                </a>
                <a href="#consultation" class="cta-button">
                    <i class="fas fa-comments"></i> 免费咨询
                </a>
            </div>

            <div style="margin-top: 60px; padding: 40px; background: rgba(255,255,255,0.1); border-radius: 20px;">
                <h3>🏁 结语：选择丰选智慧教培，选择未来</h3>
                <p style="font-size: 1.2rem; line-height: 1.8; margin-top: 20px;">
                    在教培行业数字化转型的大潮中，智慧教培管理系统不仅仅是一个工具，更是您业务腾飞的助推器。<br><br>
                    我们相信，每一个教培工作者都值得拥有更高效的工作方式，每一个学员都应该享受更优质的服务，每一个教培机构都有成为行业标杆的潜力。<br><br>
                    <strong>现在就行动，让数字化为您的事业插上腾飞的翅膀！</strong>
                </p>
                <p style="font-size: 1.5rem; font-weight: 700; margin-top: 30px; color: #81e6d9;">
                    智慧校园管理系统 - 专为教培而生，因专业而精彩！
                </p>
            </div>
        </div>
    </section>

    <!-- JavaScript for Interactions -->
    <script>
        // Navbar scroll effect
        window.addEventListener('scroll', () => {
            const navbar = document.querySelector('.navbar');
            const progressBar = document.querySelector('.progress-bar');

            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }

            // Progress bar
            const scrolled = (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100;
            progressBar.style.width = scrolled + '%';
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe all animated elements
        document.querySelectorAll('.fade-in-up, .scenario, .feature-card, .advantage-card, .value-card').forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(30px)';
            el.style.transition = 'all 0.6s ease';
            observer.observe(el);
        });

        // Add staggered animation delays
        document.querySelectorAll('.features-grid .feature-card').forEach((card, index) => {
            card.style.transitionDelay = `${index * 0.1}s`;
        });

        document.querySelectorAll('.advantages-grid .advantage-card').forEach((card, index) => {
            card.style.transitionDelay = `${index * 0.2}s`;
        });

        // Floating elements enhanced animation
        document.querySelectorAll('.floating-element').forEach((el, index) => {
            el.style.animationDelay = `${index * -1.5}s`;
            el.style.animationDuration = `${6 + index}s`;
        });

        // Add parallax effect to hero section
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const parallax = document.querySelector('.hero');
            const speed = scrolled * 0.5;

            if (parallax) {
                parallax.style.transform = `translateY(${speed}px)`;
            }
        });

        // Add typing effect to hero title
        function typeWriter(element, text, speed = 100) {
            let i = 0;
            element.innerHTML = '';

            function type() {
                if (i < text.length) {
                    element.innerHTML += text.charAt(i);
                    i++;
                    setTimeout(type, speed);
                }
            }
            type();
        }

        // Initialize typing effect when page loads
        window.addEventListener('load', () => {
            const heroTitle = document.querySelector('.hero h1');
            if (heroTitle) {
                const originalText = heroTitle.textContent;
                typeWriter(heroTitle, originalText, 80);
            }
        });
    </script>
</body>
</html>
